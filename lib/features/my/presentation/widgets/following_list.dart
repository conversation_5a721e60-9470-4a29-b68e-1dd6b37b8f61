import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/localization_utils.dart';
import 'package:flutter_audio_room/core/widgets/app_button.dart';
import 'package:flutter_audio_room/core/widgets/refresh_list_view.dart';
import 'package:flutter_audio_room/features/chat/data/model/user_online_status_model.dart';
import 'package:flutter_audio_room/features/chat/data/model/user_room_status_model.dart';
import 'package:flutter_audio_room/features/my/data/model/follow_user_info_model.dart';
import 'package:flutter_audio_room/features/my/presentation/provider/follow_provider.dart';
import 'package:flutter_audio_room/features/my/presentation/widgets/user_list_item.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 关注列表组件
///
/// 用于显示用户的关注列表，支持分页加载、刷新和取消关注操作
class FollowingList extends ConsumerStatefulWidget {
  /// 点击用户项时的回调
  final Function(FollowUserInfoModel user)? onUserTap;

  /// 是否显示取消关注按钮
  final bool showFollowButton;

  /// 自定义用户项构建器
  final Widget Function(BuildContext context, FollowUserInfoModel user)?
      itemBuilder;

  /// 是否仅显示互相关注的用户
  final bool onlyMutualFollow;

  const FollowingList({
    super.key,
    this.onUserTap,
    this.showFollowButton = true,
    this.itemBuilder,
    this.onlyMutualFollow = false,
  });

  @override
  ConsumerState<FollowingList> createState() => _FollowingListState();
}

class _FollowingListState extends ConsumerState<FollowingList> {
  String? _errorMessage;
  List<FollowUserInfoModel> _dataList = [];
  bool _isLoading = false;
  dynamic _error;
  bool _hasMore = true;
  int _currentPage = 1;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _onRefresh();
    });
  }

  Future<void> _onRefresh() async {
    _currentPage = 1;
    _error = null;
    await _fetchFollowing();
  }

  Future<void> _onLoadMore() async {
    if (!_hasMore || _isLoading) return;
    _currentPage++;
    await _fetchFollowing();
  }

  Future<void> _fetchFollowing() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final defaultErrorMessage = context.l10n.loadFollowingListFailed;
      final result = await ref.read(followProvider.notifier).loadFollowing(
            current: _currentPage,
          );

      if (result.isLeft()) {
        setState(() {
          _errorMessage = result.getLeft()?.message;
        });
        throw result.getLeft()?.message ?? defaultErrorMessage;
      }

      final following = result.getRight();
      if (following == null) {
        setState(() {
          _errorMessage = context.l10n.loadFollowingListFailed;
        });
        throw defaultErrorMessage;
      }

      final records = following.records;
      // 过滤互相关注的用户
      final filteredList = _getFilteredFollowingList(records);

      setState(() {
        if (_currentPage == 1) {
          _dataList = filteredList;
        } else {
          _dataList.addAll(filteredList);
        }
        _hasMore = filteredList.isNotEmpty;
        _error = null;
      });
    } catch (error) {
      setState(() {
        _error = error;
        if (_currentPage > 1) {
          _currentPage--; // 回退页码
        }
      });
      rethrow;
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _unfollowUser(FollowUserInfoModel user) async {
    if (user.profile?.id == null) return;

    final result = await ref.read(followProvider.notifier).unFollowUser(
          user.profile!.id!.toString(),
        );

    if (result.isLeft() && mounted) {
      LoadingUtils.showToast(result.getLeft()!.message);
    }
  }

  Widget _buildItemWidget(
      BuildContext context, FollowUserInfoModel user, int index) {
    if (widget.itemBuilder != null) {
      final customItem = widget.itemBuilder!(context, user);
      return GestureDetector(
        key: Key(user.profile?.id ?? ''),
        onTap: () => widget.onUserTap?.call(user),
        child: customItem,
      );
    }

    return UserListItem(
      key: Key(user.profile?.id ?? ''),
      user: user,
      onTap: widget.onUserTap != null ? () => widget.onUserTap!(user) : null,
      showActionButton: widget.showFollowButton,
      userOnlineStatus: user.isOnline
          ? UserOnlineStatusModel(
              userId: user.profile?.id,
              status: user.onlineStatus,
            )
          : null,
      userRoomStatus: user.isLive
          ? UserRoomStatusModel(
              userId: user.profile?.id,
              roomId: user.roomId,
              status: 1, // 1 means user is in a live room
            )
          : null,
      actionButton: widget.showFollowButton ? _buildUnfollowButton(user) : null,
    );
  }

  Widget _buildUnfollowButton(FollowUserInfoModel user) {
    // 关注列表的按钮逻辑：显示取消关注按钮
    return AppButton(
      onPressed: () async {
        await _unfollowUser(user);
      },
      type: AppButtonType.outline,
      text: context.l10n.following,
      height: null,
      padding: EdgeInsets.symmetric(
        horizontal: 15.w,
        vertical: 4.h,
      ),
      borderRadius: 6.r,
      foregroundColor: context.colorScheme.outline,
      textStyle: (context, defaultStyle) =>
          context.theme.textTheme.bodySmall?.copyWith(
            color: context.colorScheme.outline,
          ) ??
          defaultStyle,
    );
  }

  Widget _buildItemSeparator(BuildContext context, int index) {
    return Column(
      children: [
        12.verticalSpace,
        Divider(
          height: 1,
          thickness: 1,
          color: context.colorScheme.outline.withValues(alpha: 0.2),
        ),
        12.verticalSpace,
      ],
    );
  }

  List<FollowUserInfoModel> _getFilteredFollowingList(
      List<FollowUserInfoModel> followingList) {
    if (widget.onlyMutualFollow) {
      // 只显示互相关注的用户
      return followingList
          .where((user) => user.isMutualFollow == true)
          .toList();
    }
    return followingList;
  }

  @override
  Widget build(BuildContext context) {
    return RefreshListView<FollowUserInfoModel>(
      dataList: _dataList,
      isLoading: _isLoading,
      error: _error,
      hasMore: _hasMore,
      onRefresh: _onRefresh,
      onLoadMore: _onLoadMore,
      itemBuilder: _buildItemWidget,
      separatorBuilder: _buildItemSeparator,
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      emptyBuilder: (context) => Center(
        child: Text(context.l10n.noFollowing),
      ),
      errorBuilder: (context, error) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _errorMessage ?? context.l10n.loadFollowingListFailed,
              style: TextStyle(color: context.colorScheme.error),
            ),
            TextButton(
              onPressed: _onRefresh,
              child: Text(context.l10n.retry),
            ),
          ],
        ),
      ),
    );
  }
}
