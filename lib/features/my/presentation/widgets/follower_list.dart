import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/localization_utils.dart';
import 'package:flutter_audio_room/core/widgets/app_button.dart';
import 'package:flutter_audio_room/core/widgets/refresh_list_view.dart';
import 'package:flutter_audio_room/features/chat/data/model/user_online_status_model.dart';
import 'package:flutter_audio_room/features/chat/data/model/user_room_status_model.dart';
import 'package:flutter_audio_room/features/my/data/model/follow_user_info_model.dart';
import 'package:flutter_audio_room/features/my/domain/entities/follow_origin.dart';
import 'package:flutter_audio_room/features/my/presentation/provider/follow_provider.dart';
import 'package:flutter_audio_room/features/my/presentation/widgets/user_list_item.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 粉丝列表组件
///
/// 用于显示用户的粉丝列表，支持分页加载、刷新和关注/取消关注操作
class FollowerList extends ConsumerStatefulWidget {
  /// 点击用户项时的回调
  final Function(FollowUserInfoModel user)? onUserTap;

  /// 是否显示关注/取消关注按钮
  final bool showFollowButton;

  /// 自定义用户项构建器
  final Widget Function(BuildContext context, FollowUserInfoModel user)?
      itemBuilder;

  const FollowerList({
    super.key,
    this.onUserTap,
    this.showFollowButton = true,
    this.itemBuilder,
  });

  @override
  ConsumerState<FollowerList> createState() => _FollowerListState();
}

class _FollowerListState extends ConsumerState<FollowerList> {
  String? _errorMessage;
  List<FollowUserInfoModel> _dataList = [];
  bool _isLoading = false;
  dynamic _error;
  bool _hasMore = true;
  int _currentPage = 1;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _onRefresh();
    });
  }

  Future<void> _onRefresh() async {
    _currentPage = 1;
    _error = null;
    await _fetchFollowers();
  }

  Future<void> _onLoadMore() async {
    if (!_hasMore || _isLoading) return;
    _currentPage++;
    await _fetchFollowers();
  }

  Future<void> _fetchFollowers() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final defaultErrorMessage = context.l10n.loadFollowingListFailed;
      final result = await ref.read(followProvider.notifier).loadFollowers(
            current: _currentPage,
          );

      if (result.isLeft()) {
        setState(() {
          _errorMessage = result.getLeft()?.message;
        });
        throw result.getLeft()?.message ?? defaultErrorMessage;
      }

      final followers = result.getRight();
      if (followers == null) {
        setState(() {
          _errorMessage = context.l10n.loadFollowingListFailed;
        });
        throw defaultErrorMessage;
      }

      final records = followers.records;
      setState(() {
        if (_currentPage == 1) {
          _dataList = records;
        } else {
          _dataList.addAll(records);
        }
        _hasMore = records.isNotEmpty;
        _error = null;
      });
    } catch (error) {
      setState(() {
        _error = error;
        if (_currentPage > 1) {
          _currentPage--; // 回退页码
        }
      });
      rethrow;
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _unfollowUser(FollowUserInfoModel user) async {
    if (user.profile?.id == null) return;

    final result = await ref.read(followProvider.notifier).unFollowUser(
          user.profile!.id!.toString(),
        );

    if (result.isLeft() && mounted) {
      LoadingUtils.showToast(result.getLeft()!.message);
    }
  }

  Future<void> _followUser(FollowUserInfoModel user) async {
    if (user.profile?.id == null) return;

    final result = await ref.read(followProvider.notifier).followUser(
          user.profile!.id!.toString(),
          origin: FollowOrigin.profile,
        );

    if (result.isLeft() && mounted) {
      LoadingUtils.showToast(result.getLeft()?.message ?? 'Failed to follow');
    }
  }

  Widget _buildItemWidget(
      BuildContext context, FollowUserInfoModel user, int index) {
    if (widget.itemBuilder != null) {
      final customItem = widget.itemBuilder!(context, user);
      return GestureDetector(
        key: Key(user.profile?.id ?? ''),
        onTap: () => widget.onUserTap?.call(user),
        child: customItem,
      );
    }

    return UserListItem(
      key: Key(user.profile?.id ?? ''),
      user: user,
      onTap: widget.onUserTap != null ? () => widget.onUserTap!(user) : null,
      showActionButton: widget.showFollowButton,
      userOnlineStatus: user.isOnline
          ? UserOnlineStatusModel(
              userId: user.profile?.id,
              status: user.onlineStatus,
            )
          : null,
      userRoomStatus: user.isLive
          ? UserRoomStatusModel(
              userId: user.profile?.id,
              roomId: user.roomId,
              status: 1, // 1 means user is in a live room
            )
          : null,
      actionButton: widget.showFollowButton
          ? _buildFollowButton(user) : null,
    );
  }

  Widget _buildFollowButton(FollowUserInfoModel user) {
    // 粉丝列表的关注按钮逻辑：粉丝可能已经被我关注
    final isMutualFollow = user.isMutualFollow == true; // 处理null情况
    
    return AppButton(
      onPressed: () async {
        if (isMutualFollow) {
          await _unfollowUser(user);
        } else {
          await _followUser(user);
        }
      },
      type: AppButtonType.outline,
      text: isMutualFollow ? context.l10n.following : context.l10n.follow,
      height: null,
      padding: EdgeInsets.symmetric(
        horizontal: 15.w,
        vertical: 4.h,
      ),
      borderRadius: 6.r,
      foregroundColor: isMutualFollow
          ? context.colorScheme.outline
          : context.colorScheme.primary,
      textStyle: (context, defaultStyle) =>
          context.theme.textTheme.bodySmall?.copyWith(
            color: isMutualFollow
                ? context.colorScheme.outline
                : context.colorScheme.primary,
          ) ??
          defaultStyle,
    );
  }

  Widget _buildItemSeparator(BuildContext context, int index) {
    return Column(
      children: [
        12.verticalSpace,
        Divider(
          height: 1,
          thickness: 1,
          color: context.colorScheme.outline.withValues(alpha: 0.2),
        ),
        12.verticalSpace,
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return RefreshListView<FollowUserInfoModel>(
      dataList: _dataList,
      isLoading: _isLoading,
      error: _error,
      hasMore: _hasMore,
      onRefresh: _onRefresh,
      onLoadMore: _onLoadMore,
      itemBuilder: _buildItemWidget,
      separatorBuilder: _buildItemSeparator,
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      emptyBuilder: (context) => Center(
        child: Text(context.l10n.noFollower),
      ),
      errorBuilder: (context, error) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _errorMessage ?? context.l10n.loadFollowingListFailed,
              style: TextStyle(color: context.colorScheme.error),
            ),
            TextButton(
              onPressed: _onRefresh,
              child: Text(context.l10n.retry),
            ),
          ],
        ),
      ),
    );
  }
}
