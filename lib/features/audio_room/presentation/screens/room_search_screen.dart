import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/utils/localization_utils.dart';
import 'package:flutter_audio_room/core/widgets/app_button.dart';
import 'package:flutter_audio_room/core/widgets/refresh_list_view.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_list_item.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/join_room_mixin.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/audio_room_card.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class RoomSearchScreen extends ConsumerStatefulWidget {
  const RoomSearchScreen({super.key});

  @override
  ConsumerState<RoomSearchScreen> createState() => _RoomSearchScreenState();
}

class _RoomSearchScreenState extends ConsumerState<RoomSearchScreen>
    with JoinRoomMixin {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<List<RoomListItem>> _fetchRooms(int page) async {
    if (_searchController.text.isEmpty) {
      return [];
    }

    final result = await ref.read(audioRoomProvider.notifier).searchRooms(
          keyword: _searchController.text,
          isRefresh: page == 1,
        );

    return result.fold(
      (error) => throw error,
      (rooms) => rooms,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.l10n.search),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Container(
                    alignment: Alignment.centerLeft,
                    height: 40.h,
                    padding:
                        EdgeInsets.symmetric(horizontal: 16.w, vertical: 5.h),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: context.colorScheme.outline),
                    ),
                    child: TextField(
                      controller: _searchController,
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        hintText: 'Input the keyword',
                      ),
                    ),
                  ),
                ),
                10.horizontalSpace,
                AppButton(
                  onPressed: () {
                    setState(() {});
                  },
                  child: Icon(
                    Icons.search,
                    color: context.colorScheme.onPrimary,
                  ),
                ),
              ],
            ),
            12.verticalSpace,
            _buildSearchList(),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchList() {
    // 这里使用key来确保当搜索关键词变化时能够重新创建RefreshListView实例
    return Expanded(
      child: RefreshListView<RoomListItem>(
        key: ValueKey(_searchController.text),
        onLoadPage: _fetchRooms,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 1.45,
          crossAxisSpacing: 12.w,
          mainAxisSpacing: 16.h,
        ),
        emptyBuilder: (context) => Center(
          child: Text(context.l10n.noRoomsFound),
        ),
        errorBuilder: (context, error) => Center(
          child: Text(error.toString()),
        ),
        itemBuilder: (context, item, index) => GestureDetector(
            onTap: () => navigateToAudioRoom(item.room?.id ?? ''),
            child: RoomCard(item: item)),
      ),
    );
  }
}
